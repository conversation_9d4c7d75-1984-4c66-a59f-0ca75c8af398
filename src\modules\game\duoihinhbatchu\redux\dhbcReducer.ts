import {createSlice} from '@reduxjs/toolkit';
import {DHBCGameConfig, Question} from '../models/models';
import {dhbcDa} from '../da/dhbcDA';
import {loadGameConfig, loadGameQuestions} from './dhbcAsyncThunk';

const dhbcDA = new dhbcDa();

interface State {
  dataListQuestion: Question[];
  listQuestion: Question[];
  currentQuestion: Question;
  totalQuestion: number;
  questionDone: number;
  isWinLevel: boolean;
  currentLevel: number;
  // API states
  loading: boolean;
  configLoading: boolean;
  error: string | null;
  configError: string | null;
  initialized: boolean;
  configInitialized: boolean;
  noData: boolean;
  // Game config from API
  usedHints: string[];
  configLv1: DHBCGameConfig | null;
  configLv2: DHBCGameConfig | null;
  configLv3: DHBCGameConfig | null;
}

const data = [
  {
    id: '1',
    text: 'Đ<PERSON>y là con gì',
    image:
      'https://pethouse.com.vn/wp-content/uploads/2023/06/cho-pug-1256x800.webp',
    hint: 'Đây là một loài vật có vú và có thể ăn thịt',
    answer: 'con chó',
  },
  {
    id: '2',
    text: 'Đây là con gì',
    image:
      'https://topanh.com/wp-content/uploads/2024/02/Hinh-anh-con-khi-dep-nhat.jpg',
    hint: 'Đây là một loài động vật có thể leo trèo',
    answer: 'con khỉ',
  },
];
const initialState: State = {
  dataListQuestion: [],
  listQuestion: [],
  currentQuestion: {} as Question,
  totalQuestion: 0,
  questionDone: 0,
  isWinLevel: false,
  currentLevel: 0,
  // API states
  loading: false,
  configLoading: false,
  error: null,
  configError: null,
  initialized: false,
  configInitialized: false,
  noData: false,
  // Game config
  usedHints: [],
  configLv1: null,
  configLv2: null,
  configLv3: null,
};

// Helper function to get random questions from array
const getRandomQuestions = (questions: Question[], count: number = 5): Question[] => {
  if (questions.length <= count) {
    return [...questions]; // Return all if less than or equal to count
  }

  const shuffled = [...questions].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const dhbcReducer = createSlice({
  name: 'dhbcReducer',
  initialState,
  reducers: {
    setData(state, action: {payload: {stateName: keyof State; value: any}}) {
      (state as any)[action.payload.stateName] = action.payload.value;
    },
    startGame(state) {
      // Reset game state
      state.currentLevel = 1;
      const allQuestionsForLevel = state.dataListQuestion.filter(
        question => question.level === state.currentLevel,
      );
      // Get random 5 questions for this level
      const questionLevel = getRandomQuestions(allQuestionsForLevel, 5);
      state.listQuestion = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.totalQuestion = questionLevel.length;
      state.questionDone = 0;
      state.isWinLevel = false;
      state.usedHints = [];
    },
    nextQuestion(state) {
      state.questionDone += 1;
      state.currentQuestion = state.listQuestion[state.questionDone];
    },
    nextLevel: state => {
      state.currentLevel = state.currentLevel + 1;
      const allQuestionsForLevel = state.dataListQuestion.filter(
        question => question.level === state.currentLevel,
      );
      // Get random 5 questions for this level
      const questionLevel = getRandomQuestions(allQuestionsForLevel, 5);
      state.listQuestion = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.questionDone = 0;
      state.totalQuestion = questionLevel.length;
    },
    reset(_state) {
      // Reset về initial state
      return initialState;
    },
    markHintUsed(state, action) {
      const questionId = action.payload;
      if (!state.usedHints.includes(questionId)) {
        state.usedHints.push(questionId);
      }
    },
  },
  extraReducers: builder => {
    // Handle getGameConfig
    builder
      .addCase(loadGameConfig.pending, state => {
        state.configLoading = true;
        state.configError = null;
      })
      .addCase(loadGameConfig.fulfilled, (state, action) => {
        state.configLoading = false;
        state.configLv1 = action.payload.configLv1;
        state.configLv2 = action.payload.configLv2;
        state.configLv3 = action.payload.configLv3;
        state.configInitialized = true;
        state.configError = null;
      })
      .addCase(loadGameConfig.rejected, (state, action) => {
        state.configLoading = false;
        state.configError =
          action.error.message || 'Failed to load game config';
        state.configInitialized = false;
      })
      // Handle loadDHBCQuestions
      .addCase(loadGameQuestions.pending, state => {
        state.loading = true;
        state.error = null;
        state.noData = false;
      })
      .addCase(loadGameQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.dataListQuestion = action.payload.questions;
        state.initialized = true;
        state.error = null;
        // Check if we have data
        if (action.payload.questions.length === 0) {
          state.noData = true;
        } else {
          state.noData = false;
        }
      })
      .addCase(loadGameQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to load questions';
        state.initialized = false;
        state.noData = true;
      });
  },
});

export const {
  setData,
  reset,
  startGame,
  nextQuestion,
  markHintUsed,
  nextLevel,
} = dhbcReducer.actions;

export default dhbcReducer.reducer;
